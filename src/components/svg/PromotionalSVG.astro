---
import SVGWrapper from './SVGWrapper.astro';

export interface Props {
  class?: string;
  title?: string;
  subtitle?: string;
  discount?: string;
  ctaText?: string;
  brandName?: string;
  interactive?: boolean;
  theme?: 'sale' | 'new' | 'limited' | 'seasonal';
  fullScreen?: boolean;
}

const {
  class: className = "w-full h-full",
  title = "FLASH SALE",
  subtitle = "Limited Time Offer",
  discount = "50% OFF",
  ctaText = "Shop Now",
  brandName = "Fashion Store",
  interactive = false,
  theme = 'sale',
  fullScreen = false
} = Astro.props;

// Theme-based colors
const themeColors = {
  sale: {
    primary: 'var(--svg-error-500)',
    secondary: 'var(--svg-error-600)',
    accent: 'var(--svg-accent-400)',
    bg: 'var(--svg-error-50)'
  },
  new: {
    primary: 'var(--svg-primary-500)',
    secondary: 'var(--svg-primary-600)',
    accent: 'var(--svg-accent-400)',
    bg: 'var(--svg-primary-50)'
  },
  limited: {
    primary: 'var(--svg-warning-500)',
    secondary: 'var(--svg-warning-600)',
    accent: 'var(--svg-error-400)',
    bg: 'var(--svg-warning-50)'
  },
  seasonal: {
    primary: 'var(--svg-success-500)',
    secondary: 'var(--svg-success-600)',
    accent: 'var(--svg-accent-400)',
    bg: 'var(--svg-success-50)'
  }
};

const colors = themeColors[theme];
---

<SVGWrapper
  class={className}
  viewBox="0 0 360 640"
>
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="promo-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color={colors.primary} />
      <stop offset="100%" stop-color={colors.secondary} />
    </linearGradient>
    
    <linearGradient id="promo-accent" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color={colors.accent} stop-opacity="0.8" />
      <stop offset="50%" stop-color={colors.accent} stop-opacity="1" />
      <stop offset="100%" stop-color={colors.accent} stop-opacity="0.8" />
    </linearGradient>
    
    <radialGradient id="spotlight" cx="50%" cy="30%" r="60%">
      <stop offset="0%" stop-color="var(--svg-bg-primary)" stop-opacity="0.3" />
      <stop offset="100%" stop-color="var(--svg-bg-primary)" stop-opacity="0" />
    </radialGradient>
    
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="0" dy="0"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.8"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Sparkle pattern -->
    <pattern id="sparkles" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="var(--svg-bg-primary)" opacity="0.6">
        {interactive && <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>}
      </circle>
      <circle cx="30" cy="25" r="1.5" fill="var(--svg-bg-primary)" opacity="0.4">
        {interactive && <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>}
      </circle>
      <circle cx="5" cy="35" r="0.8" fill="var(--svg-bg-primary)" opacity="0.7">
        {interactive && <animate attributeName="opacity" values="0.7;1;0.7" dur="1.5s" repeatCount="indefinite"/>}
      </circle>
    </pattern>
  </defs>

  <!-- Background -->
  <rect width="360" height="640" fill="var(--svg-bg-secondary)"/>

  <!-- Header -->
  <rect x="0" y="0" width="360" height="80" fill={colors.primary}/>
  <circle cx="40" cy="40" r="20" fill="var(--svg-bg-primary)" opacity="0.9"/>
  <text x="80" y="45" fill="var(--svg-bg-primary)" font-family="system-ui" font-size="18" font-weight="600">{brandName}</text>
  <text x="80" y="65" fill="var(--svg-bg-primary)" opacity="0.8" font-family="system-ui" font-size="12">Online</text>

  <!-- Welcome Message -->
  <g transform="translate(16, 96)">
    <rect width="280" height="50" rx="16" fill="url(#message-bg)" filter="url(#glow)"/>
    <text x="20" y="25" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">🔥 {title} Alert! {subtitle}</text>
    <text x="20" y="40" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" font-weight="600">Don't miss out on these deals!</text>
  </g>

  <!-- Customer Response -->
  <g transform="translate(80, 160)">
    <rect width="264" height="35" rx="16" fill="var(--svg-secondary-100)" filter="url(#glow)"/>
    <text x="20" y="23" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Show me the deals! 🛍️</text>
  </g>

  <!-- Promotional Card -->
  <g transform="translate(16, 210)">
    <rect width="328" height="200" rx="16" fill="url(#promo-bg)" filter="url(#glow)"/>

    <!-- Discount Badge -->
    <circle cx="80" cy="60" r="35" fill={colors.accent} stroke="var(--svg-bg-primary)" stroke-width="3" />
    <text x="80" y="55" font-family="system-ui" font-size="16" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="bold">{discount}</text>
    <text x="80" y="70" font-family="system-ui" font-size="8" fill="var(--svg-text-primary)" text-anchor="middle">OFF</text>

    <!-- Pulsing effect for discount badge -->
    {interactive && (
      <circle cx="80" cy="60" r="40" fill={colors.accent} opacity="0.3" class="animate-pulse" />
    )}

    <!-- Main Promo Text -->
    <text x="180" y="45" font-family="system-ui" font-size="20" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="bold">{title}</text>
    <text x="180" y="65" font-family="system-ui" font-size="12" fill="var(--svg-bg-primary)" text-anchor="middle" opacity="0.9">{subtitle}</text>

    <!-- Product Grid -->
    <g transform="translate(20, 90)">
      <!-- Product 1 -->
      <rect x="0" y="0" width="60" height="80" rx="8" fill="var(--svg-bg-primary)" opacity="0.9" />
      <rect x="5" y="5" width="50" height="40" rx="4" fill="var(--svg-secondary-200)" />
      <text x="30" y="60" font-family="system-ui" font-size="10" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Shirt</text>
      <text x="30" y="75" font-family="system-ui" font-size="10" fill={colors.primary} text-anchor="middle" font-weight="bold">$29.99</text>

      <!-- Product 2 -->
      <rect x="70" y="0" width="60" height="80" rx="8" fill="var(--svg-bg-primary)" opacity="0.9" />
      <rect x="75" y="5" width="50" height="40" rx="4" fill="var(--svg-secondary-200)" />
      <text x="100" y="60" font-family="system-ui" font-size="10" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Shoes</text>
      <text x="100" y="75" font-family="system-ui" font-size="10" fill={colors.primary} text-anchor="middle" font-weight="bold">$59.99</text>

      <!-- Product 3 -->
      <rect x="140" y="0" width="60" height="80" rx="8" fill="var(--svg-bg-primary)" opacity="0.9" />
      <rect x="145" y="5" width="50" height="40" rx="4" fill="var(--svg-secondary-200)" />
      <text x="170" y="60" font-family="system-ui" font-size="10" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Watch</text>
      <text x="170" y="75" font-family="system-ui" font-size="10" fill={colors.primary} text-anchor="middle" font-weight="bold">$199.99</text>

      <!-- Product 4 -->
      <rect x="210" y="0" width="60" height="80" rx="8" fill="var(--svg-bg-primary)" opacity="0.9" />
      <rect x="215" y="5" width="50" height="40" rx="4" fill="var(--svg-secondary-200)" />
      <text x="240" y="60" font-family="system-ui" font-size="10" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Bag</text>
      <text x="240" y="75" font-family="system-ui" font-size="10" fill={colors.primary} text-anchor="middle" font-weight="bold">$39.99</text>
    </g>
  </g>

  <!-- Action Buttons -->
  <g transform="translate(16, 430)">
    <rect width="160" height="40" rx="20" fill={colors.accent} filter="url(#glow)"/>
    <text x="80" y="26" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">{ctaText}</text>

    <rect x="172" y="0" width="156" height="40" rx="20" fill="var(--svg-secondary-300)" stroke="var(--svg-secondary-400)" stroke-width="1"/>
    <text x="250" y="26" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Browse All</text>
  </g>

  <!-- Customer Response -->
  <g transform="translate(80, 490)">
    <rect width="264" height="35" rx="16" fill="var(--svg-secondary-100)" filter="url(#glow)"/>
    <text x="20" y="23" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">I'll take the watch! Adding to cart 🛒</text>
  </g>

  <!-- Business Confirmation -->
  <g transform="translate(16, 540)">
    <rect width="280" height="50" rx="16" fill="url(#message-bg)" filter="url(#glow)"/>
    <text x="20" y="25" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Great choice! Watch added to cart.</text>
    <text x="20" y="40" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Free shipping on orders over $150! 🚚</text>
  </g>

  <!-- Typing Indicator -->
  <g transform="translate(16, 600)">
    <rect width="80" height="30" rx="15" fill="var(--svg-secondary-200)" opacity="0.8"/>
    <circle cx="30" cy="15" r="3" fill="var(--svg-text-secondary)">
      {interactive && <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0s"/>}
    </circle>
    <circle cx="40" cy="15" r="3" fill="var(--svg-text-secondary)">
      {interactive && <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.3s"/>}
    </circle>
    <circle cx="50" cy="15" r="3" fill="var(--svg-text-secondary)">
      {interactive && <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.6s"/>}
    </circle>
  </g>

  <!-- Interactive elements -->
  {interactive && (
    <>
      <!-- Shimmer effect on products -->
      <rect x="36" y="300" width="60" height="80" rx="8" fill={colors.accent} opacity="0.2" class="animate-pulse"/>
      <rect x="176" y="300" width="60" height="80" rx="8" fill={colors.accent} opacity="0.2" class="animate-pulse" style="animation-delay: 0.5s"/>

      <!-- Notification badge -->
      <g transform="translate(320, 20)">
        <circle cx="0" cy="0" r="8" fill={colors.primary}/>
        <text x="0" y="4" font-family="system-ui" font-size="10" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="bold">!</text>
      </g>

      <!-- Timer for flash sales -->
      {theme === 'sale' && (
        <g transform="translate(280, 250)">
          <rect x="-25" y="-10" width="50" height="20" rx="10" fill="var(--svg-bg-primary)" opacity="0.9" />
          <text x="0" y="2" font-family="system-ui" font-size="8" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">23:59:45</text>
          <text x="0" y="15" font-family="system-ui" font-size="6" fill="var(--svg-text-secondary)" text-anchor="middle">TIME LEFT</text>
        </g>
      )}
    </>
  )}
</SVGWrapper>

{interactive && (
  <style>
    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(400px); }
    }

    .animate-shimmer {
      animation: shimmer 3s ease-in-out infinite;
    }
  </style>
)}
