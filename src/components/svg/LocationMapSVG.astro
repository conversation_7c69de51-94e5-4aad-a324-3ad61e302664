---
import SVGWrapper from './SVGWrapper.astro';

export interface Props {
  class?: string;
  businessName?: string;
  interactive?: boolean;
  showRoute?: boolean;
  estimatedTime?: string;
}

const {
  class: className = "w-full h-full",
  businessName = "Our Store",
  interactive = false,
  showRoute = true,
  estimatedTime = "15 min"
} = Astro.props;
---

<SVGWrapper
  class={className}
  viewBox="0 0 360 640"
>
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="map-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--svg-success-50)" />
      <stop offset="100%" stop-color="var(--svg-success-100)" />
    </linearGradient>
    
    <linearGradient id="road-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="var(--svg-neutral-400)" />
      <stop offset="50%" stop-color="var(--svg-neutral-300)" />
      <stop offset="100%" stop-color="var(--svg-neutral-400)" />
    </linearGradient>
    
    <filter id="building-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
      <feOffset dx="2" dy="2"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.3"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Animated route pattern -->
    <pattern id="route-pattern" x="0" y="0" width="20" height="4" patternUnits="userSpaceOnUse">
      <rect width="20" height="4" fill="var(--svg-primary-500)"/>
      <rect x="0" y="0" width="10" height="4" fill="var(--svg-primary-600)">
        {interactive && <animateTransform attributeName="transform" type="translate" values="0,0; 20,0; 0,0" dur="2s" repeatCount="indefinite"/>}
      </rect>
    </pattern>
  </defs>

  <!-- Background -->
  <rect width="360" height="640" fill="var(--svg-bg-secondary)"/>

  <!-- Header -->
  <rect x="0" y="0" width="360" height="80" fill="var(--svg-primary-500)"/>
  <circle cx="40" cy="40" r="20" fill="var(--svg-bg-primary)" opacity="0.9"/>
  <text x="80" y="45" fill="var(--svg-bg-primary)" font-family="system-ui" font-size="18" font-weight="600">{businessName}</text>
  <text x="80" y="65" fill="var(--svg-bg-primary)" opacity="0.8" font-family="system-ui" font-size="12">Online • AI Assistant</text>

  <!-- Welcome Message -->
  <g transform="translate(16, 96)">
    <rect width="280" height="60" rx="16" fill="url(#message-bg)" filter="url(#building-shadow)"/>
    <text x="20" y="25" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Hi! I can help you find us with smart navigation 🗺️</text>
    <text x="20" y="45" font-family="system-ui" font-size="14" fill="var(--svg-text-secondary)">Would you like directions to our store?</text>
  </g>

  <!-- Customer Response -->
  <g transform="translate(80, 170)">
    <rect width="264" height="35" rx="16" fill="var(--svg-secondary-100)" filter="url(#building-shadow)"/>
    <text x="20" y="23" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Yes! Show me the best route please</text>
  </g>

  <!-- Interactive Map Card -->
  <g transform="translate(16, 220)">
    <rect width="328" height="280" rx="16" fill="var(--svg-bg-primary)" filter="url(#building-shadow)" stroke="var(--svg-neutral-200)" stroke-width="1"/>

    <!-- Map Header -->
    <rect x="16" y="16" width="296" height="40" rx="8" fill="var(--svg-primary-50)" />
    <text x="32" y="32" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)" font-weight="500">📍 Smart Navigation</text>
    <text x="32" y="46" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" font-weight="600">Route to {businessName}</text>

    <!-- Enhanced Map Area -->
    <rect x="16" y="64" width="296" height="160" rx="8" fill="var(--svg-success-50)" />

    <!-- Map Grid -->
    <defs>
      <pattern id="map-grid" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
        <path d="M 20 0 L 0 0 0 20" fill="none" stroke="var(--svg-success-200)" stroke-width="0.5"/>
      </pattern>
    </defs>
    <rect x="16" y="64" width="296" height="160" fill="url(#map-grid)" opacity="0.3"/>

    <!-- Main Roads -->
    <rect x="30" y="120" width="268" height="12" rx="6" fill="var(--svg-neutral-300)" />
    <rect x="150" y="80" width="12" height="128" rx="6" fill="var(--svg-neutral-300)" />

    <!-- Road Markings -->
    <rect x="154" y="90" width="4" height="8" fill="var(--svg-bg-primary)" />
    <rect x="154" y="110" width="4" height="8" fill="var(--svg-bg-primary)" />
    <rect x="154" y="130" width="4" height="8" fill="var(--svg-bg-primary)" />
    <rect x="154" y="150" width="4" height="8" fill="var(--svg-bg-primary)" />

    <!-- Buildings with better design -->
    <g transform="translate(50, 90)">
      <rect width="30" height="25" rx="2" fill="var(--svg-neutral-200)" stroke="var(--svg-neutral-400)" stroke-width="1"/>
      <rect x="4" y="4" width="6" height="6" fill="var(--svg-primary-200)" />
      <rect x="12" y="4" width="6" height="6" fill="var(--svg-primary-200)" />
      <rect x="20" y="4" width="6" height="6" fill="var(--svg-primary-200)" />
      <rect x="4" y="12" width="6" height="6" fill="var(--svg-primary-200)" />
      <rect x="20" y="12" width="6" height="6" fill="var(--svg-primary-200)" />
    </g>

    <g transform="translate(200, 85)">
      <rect width="35" height="30" rx="2" fill="var(--svg-neutral-200)" stroke="var(--svg-neutral-400)" stroke-width="1"/>
      <rect x="5" y="5" width="7" height="7" fill="var(--svg-primary-200)" />
      <rect x="15" y="5" width="7" height="7" fill="var(--svg-primary-200)" />
      <rect x="25" y="5" width="7" height="7" fill="var(--svg-primary-200)" />
      <rect x="5" y="15" width="7" height="7" fill="var(--svg-primary-200)" />
      <rect x="25" y="15" width="7" height="7" fill="var(--svg-primary-200)" />
    </g>

    <!-- Target Store (Enhanced) -->
    <g transform="translate(140, 140)">
      <rect width="40" height="35" rx="3" fill="var(--svg-primary-500)" stroke="var(--svg-primary-700)" stroke-width="2"/>
      <rect x="6" y="6" width="8" height="8" fill="var(--svg-bg-primary)" />
      <rect x="20" y="6" width="8" height="8" fill="var(--svg-bg-primary)" />
      <rect x="6" y="18" width="8" height="8" fill="var(--svg-bg-primary)" />
      <rect x="20" y="18" width="8" height="8" fill="var(--svg-bg-primary)" />
      <!-- Store Sign -->
      <rect x="5" y="-8" width="30" height="6" fill="var(--svg-accent-500)" rx="2" />
      <text x="20" y="-3" font-family="system-ui" font-size="8" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">STORE</text>
    </g>

    <!-- Store Location Pin (Enhanced) -->
    <g transform="translate(160, 175)">
      <circle r="15" fill="var(--svg-error-500)" stroke="var(--svg-bg-primary)" stroke-width="3" />
      <circle r="8" fill="var(--svg-bg-primary)" />
      <path d="M-3 -2 L3 -2 M0 -5 L0 1" stroke="var(--svg-error-500)" stroke-width="2" />

      <!-- Pulsing location indicator -->
      {interactive && (
        <>
          <circle r="22" fill="var(--svg-error-500)" opacity="0.3" class="animate-ping" />
          <circle r="28" fill="var(--svg-error-500)" opacity="0.2" class="animate-ping" style="animation-delay: 0.5s" />
        </>
      )}
    </g>

    <!-- Current Location -->
    <g transform="translate(80, 100)">
      <circle r="12" fill="var(--svg-primary-600)" stroke="var(--svg-bg-primary)" stroke-width="3" />
      <circle r="6" fill="var(--svg-bg-primary)" />
      {interactive && <circle r="18" fill="var(--svg-primary-600)" opacity="0.4" class="animate-pulse" />}
    </g>

    <!-- Route Path (Enhanced) -->
    {showRoute && (
      <>
        <path d="M80 100 L80 126 L160 126 L160 175"
              stroke="var(--svg-primary-600)"
              stroke-width="6"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
              opacity="0.8" />
        <path d="M80 100 L80 126 L160 126 L160 175"
              stroke="url(#route-pattern)"
              stroke-width="4"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round" />

        <!-- Route arrows -->
        <path d="M75 123 L80 126 L75 129" stroke="var(--svg-primary-700)" stroke-width="2" fill="none" stroke-linecap="round" />
        <path d="M157 170 L160 175 L163 170" stroke="var(--svg-primary-700)" stroke-width="2" fill="none" stroke-linecap="round" />
      </>
    )}

    <!-- Distance/Time Info (Enhanced) -->
    {showRoute && (
      <g transform="translate(20, 235)">
        <rect width="120" height="35" fill="var(--svg-bg-primary)" stroke="var(--svg-primary-300)" stroke-width="1" rx="6" />
        <text x="10" y="15" font-family="system-ui" font-size="10" fill="var(--svg-text-secondary)" font-weight="500">ESTIMATED ROUTE</text>
        <text x="10" y="28" font-family="system-ui" font-size="12" fill="var(--svg-text-primary)" font-weight="600">0.8 mi • {estimatedTime}</text>
      </g>
    )}

    <!-- Location Labels -->
    <text x="80" y="85" font-family="system-ui" font-size="9" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">You are here</text>
    <text x="160" y="205" font-family="system-ui" font-size="10" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">{businessName}</text>
  </g>

  <!-- Action Buttons -->
  <g transform="translate(16, 520)">
    <!-- Open in Google Maps Button -->
    <rect width="160" height="44" rx="22" fill="var(--svg-primary-500)" filter="url(#building-shadow)" class="cursor-pointer"/>
    <text x="80" y="28" font-family="system-ui" font-size="14" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="600">🗺️ Open in Maps</text>

    <!-- Share Location Button -->
    <rect x="172" y="0" width="156" height="44" rx="22" fill="var(--svg-bg-primary)" stroke="var(--svg-primary-300)" stroke-width="2" class="cursor-pointer"/>
    <text x="250" y="28" font-family="system-ui" font-size="14" fill="var(--svg-primary-600)" text-anchor="middle" font-weight="600">📤 Share Route</text>
  </g>

  <!-- AI Assistant Response -->
  <g transform="translate(16, 580)">
    <rect width="280" height="50" rx="16" fill="url(#message-bg)" filter="url(#building-shadow)"/>
    <text x="20" y="25" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Great! I've optimized the route for current traffic.</text>
    <text x="20" y="40" font-family="system-ui" font-size="14" fill="var(--svg-text-secondary)">ETA: {estimatedTime} • Tap to open in Maps 🚗</text>
  </g>
  <!-- Interactive hover overlay -->
  {interactive && (
    <rect x="0" y="0" width="360" height="640" rx="12" fill="var(--svg-primary-500)" opacity="0" class="hover:opacity-5 transition-opacity duration-300 cursor-pointer" />
  )}
</SVGWrapper>
