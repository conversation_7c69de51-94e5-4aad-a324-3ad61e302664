---
import SVGWrapper from './SVGWrapper.astro';

export interface Props {
  class?: string;
  businessName?: string;
  address?: string;
  interactive?: boolean;
  showRoute?: boolean;
  estimatedTime?: string;
  fullScreen?: boolean;
}

const {
  class: className = "w-full h-full",
  businessName = "Our Store",
  address = "123 Main Street",
  interactive = false,
  showRoute = true,
  estimatedTime = "15 min",
  fullScreen = false
} = Astro.props;
---

<SVGWrapper
  class={className}
  viewBox="0 0 360 640"
>
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="map-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--svg-success-50)" />
      <stop offset="100%" stop-color="var(--svg-success-100)" />
    </linearGradient>
    
    <linearGradient id="road-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="var(--svg-neutral-400)" />
      <stop offset="50%" stop-color="var(--svg-neutral-300)" />
      <stop offset="100%" stop-color="var(--svg-neutral-400)" />
    </linearGradient>
    
    <filter id="building-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
      <feOffset dx="2" dy="2"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.3"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Animated route pattern -->
    <pattern id="route-pattern" x="0" y="0" width="20" height="4" patternUnits="userSpaceOnUse">
      <rect width="20" height="4" fill="var(--svg-primary-500)"/>
      <rect x="0" y="0" width="10" height="4" fill="var(--svg-primary-600)">
        {interactive && <animateTransform attributeName="transform" type="translate" values="0,0; 20,0; 0,0" dur="2s" repeatCount="indefinite"/>}
      </rect>
    </pattern>
  </defs>

  <!-- Background -->
  <rect width="360" height="640" fill="var(--svg-bg-secondary)"/>

  <!-- Header -->
  <rect x="0" y="0" width="360" height="80" fill="var(--svg-primary-500)"/>
  <circle cx="40" cy="40" r="20" fill="var(--svg-bg-primary)" opacity="0.9"/>
  <text x="80" y="45" fill="var(--svg-bg-primary)" font-family="system-ui" font-size="18" font-weight="600">{businessName}</text>
  <text x="80" y="65" fill="var(--svg-bg-primary)" opacity="0.8" font-family="system-ui" font-size="12">Online</text>

  <!-- Welcome Message -->
  <g transform="translate(16, 96)">
    <rect width="280" height="50" rx="16" fill="url(#message-bg)" filter="url(#building-shadow)"/>
    <text x="20" y="25" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Hi! Need directions to our store? 📍</text>
    <text x="20" y="40" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">I can help you navigate here!</text>
  </g>

  <!-- Customer Response -->
  <g transform="translate(80, 160)">
    <rect width="264" height="35" rx="16" fill="var(--svg-secondary-100)" filter="url(#building-shadow)"/>
    <text x="20" y="23" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Yes please! Where exactly are you?</text>
  </g>

  <!-- Location Share Card -->
  <g transform="translate(16, 210)">
    <rect width="328" height="200" rx="16" fill="url(#map-bg)" filter="url(#building-shadow)"/>

    <!-- Mini Map -->
    <rect x="16" y="16" width="296" height="120" rx="8" fill="var(--svg-success-200)" opacity="0.6" />

    <!-- Roads -->
    <rect x="30" y="60" width="268" height="8" fill="var(--svg-neutral-400)" />
    <rect x="150" y="30" width="8" height="90" fill="var(--svg-neutral-400)" />

    <!-- Buildings -->
    <rect x="50" y="40" width="20" height="15" fill="var(--svg-neutral-300)" />
    <rect x="180" y="35" width="25" height="20" fill="var(--svg-neutral-300)" />

    <!-- Store Location (highlighted) -->
    <rect x="140" y="80" width="25" height="20" fill="var(--svg-primary-500)" />
    <circle cx="152" cy="90" r="8" fill="var(--svg-error-500)" stroke="var(--svg-bg-primary)" stroke-width="2" />
    <circle cx="152" cy="90" r="4" fill="var(--svg-bg-primary)" />

    <!-- Pulsing location indicator -->
    {interactive && (
      <>
        <circle cx="152" cy="90" r="12" fill="var(--svg-error-500)" opacity="0.3" class="animate-ping" />
        <circle cx="152" cy="90" r="18" fill="var(--svg-error-500)" opacity="0.2" class="animate-ping" style="animation-delay: 0.5s" />
      </>
    )}

    <!-- Address Info -->
    <text x="164" y="155" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">{businessName}</text>
    <text x="164" y="175" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)" text-anchor="middle">{address}</text>
  </g>

  <!-- Action Buttons -->
  <g transform="translate(16, 430)">
    <!-- Open in Google Maps Button -->
    <rect width="160" height="40" rx="20" fill="var(--svg-primary-500)" filter="url(#building-shadow)" class="cursor-pointer"/>
    <text x="80" y="26" font-family="system-ui" font-size="14" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="600">Open in Maps</text>

    <!-- Share Location Button -->
    <rect x="172" y="0" width="156" height="40" rx="20" fill="var(--svg-secondary-300)" stroke="var(--svg-secondary-400)" stroke-width="1" class="cursor-pointer"/>
    <text x="250" y="26" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Share Location</text>
  </g>

  <!-- Coordinates Display -->
  <g transform="translate(16, 490)">
    <rect width="328" height="50" rx="16" fill="var(--svg-neutral-100)" filter="url(#building-shadow)"/>
    <text x="20" y="25" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)">📍 Coordinates:</text>
    <text x="20" y="40" font-family="system-ui" font-size="12" fill="var(--svg-text-primary)" font-weight="600">40.7128° N, 74.0060° W</text>

    <!-- Copy button -->
    <rect x="280" y="15" width="30" height="20" rx="10" fill="var(--svg-primary-100)" class="cursor-pointer"/>
    <text x="295" y="27" font-family="system-ui" font-size="10" fill="var(--svg-primary-600)" text-anchor="middle" font-weight="600">Copy</text>
  </g>

  <!-- Customer Response -->
  <g transform="translate(80, 560)">
    <rect width="264" height="35" rx="16" fill="var(--svg-secondary-100)" filter="url(#building-shadow)"/>
    <text x="20" y="23" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Perfect! Opening in Google Maps now 🗺️</text>
  </g>

  <!-- Typing Indicator -->
  <g transform="translate(16, 610)">
    <rect width="80" height="30" rx="15" fill="var(--svg-secondary-200)" opacity="0.8"/>
    <circle cx="30" cy="15" r="3" fill="var(--svg-text-secondary)">
      {interactive && <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0s"/>}
    </circle>
    <circle cx="40" cy="15" r="3" fill="var(--svg-text-secondary)">
      {interactive && <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.3s"/>}
    </circle>
    <circle cx="50" cy="15" r="3" fill="var(--svg-text-secondary)">
      {interactive && <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.6s"/>}
    </circle>
  </g>
  
  <!-- Target Store (highlighted) -->
  <rect x="200" y="150" width="60" height="50" fill="var(--svg-primary-500)" stroke="var(--svg-primary-700)" stroke-width="2" filter="url(#building-shadow)" />
  <rect x="210" y="160" width="12" height="12" fill="var(--svg-bg-primary)" />
  <rect x="230" y="160" width="12" height="12" fill="var(--svg-bg-primary)" />
  <rect x="210" y="180" width="12" height="12" fill="var(--svg-bg-primary)" />
  <rect x="230" y="180" width="12" height="12" fill="var(--svg-bg-primary)" />
  
  <!-- Store Sign -->
  <rect x="205" y="140" width="50" height="8" fill="var(--svg-accent-500)" rx="2" />
  <text x="230" y="147" font-family="system-ui" font-size="6" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">STORE</text>
  
  <!-- Store Location Pin -->
  <circle cx="230" cy="175" r="12" fill="var(--svg-error-500)" stroke="var(--svg-bg-primary)" stroke-width="2" />
  <circle cx="230" cy="175" r="6" fill="var(--svg-bg-primary)" />
  <path d="M230 172 L230 178 M227 175 L233 175" stroke="var(--svg-error-500)" stroke-width="2" />
  
  <!-- Pulsing location indicator -->
  {interactive && (
    <>
      <circle cx="230" cy="175" r="18" fill="var(--svg-error-500)" opacity="0.3" class="animate-ping" />
      <circle cx="230" cy="175" r="24" fill="var(--svg-error-500)" opacity="0.2" class="animate-ping" style="animation-delay: 0.5s" />
    </>
  )}
  
  <!-- Building 3 -->
  <rect x="300" y="60" width="50" height="50" fill="var(--svg-neutral-200)" stroke="var(--svg-neutral-400)" stroke-width="1" filter="url(#building-shadow)" />
  <rect x="310" y="70" width="10" height="10" fill="var(--svg-primary-200)" />
  <rect x="330" y="70" width="10" height="10" fill="var(--svg-primary-200)" />
  <rect x="310" y="90" width="10" height="10" fill="var(--svg-primary-200)" />
  <rect x="330" y="90" width="10" height="10" fill="var(--svg-primary-200)" />
  
  <!-- Building 4 -->
  <rect x="50" y="150" width="40" height="60" fill="var(--svg-neutral-200)" stroke="var(--svg-neutral-400)" stroke-width="1" filter="url(#building-shadow)" />
  <rect x="55" y="160" width="8" height="8" fill="var(--svg-primary-200)" />
  <rect x="67" y="160" width="8" height="8" fill="var(--svg-primary-200)" />
  <rect x="79" y="160" width="8" height="8" fill="var(--svg-primary-200)" />
  
  <!-- Current Location -->
  <circle cx="120" cy="80" r="10" fill="var(--svg-primary-600)" stroke="var(--svg-bg-primary)" stroke-width="2" />
  <circle cx="120" cy="80" r="4" fill="var(--svg-bg-primary)" />
  
  <!-- Current location pulse -->
  {interactive && (
    <circle cx="120" cy="80" r="15" fill="var(--svg-primary-600)" opacity="0.4" class="animate-pulse" />
  )}
  
  <!-- Route Path -->
  {showRoute && (
    <>
      <path d="M120 80 L120 128 L230 128 L230 175" 
            stroke="url(#route-pattern)" 
            stroke-width="4" 
            fill="none" 
            stroke-linecap="round" 
            stroke-linejoin="round" />
      
      <!-- Route arrows -->
      <path d="M115 125 L120 128 L115 131" stroke="var(--svg-primary-600)" stroke-width="2" fill="none" stroke-linecap="round" />
      <path d="M227 170 L230 175 L233 170" stroke="var(--svg-primary-600)" stroke-width="2" fill="none" stroke-linecap="round" />
    </>
  )}
  
  <!-- Street Labels -->
  <text x="200" y="20" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)" text-anchor="middle" font-weight="500">Main Street</text>
  <text x="150" y="35" font-family="system-ui" font-size="10" fill="var(--svg-text-tertiary)" text-anchor="middle">Cross St</text>
  
  <!-- Location Labels -->
  <text x="120" y="60" font-family="system-ui" font-size="10" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">You are here</text>
  <text x="230" y="220" font-family="system-ui" font-size="12" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">{businessName}</text>
  <text x="230" y="235" font-family="system-ui" font-size="10" fill="var(--svg-text-secondary)" text-anchor="middle">{address}</text>
  
  <!-- Distance/Time Info -->
  {showRoute && (
    <rect x="20" y="20" width="80" height="30" fill="var(--svg-bg-primary)" stroke="var(--svg-primary-300)" stroke-width="1" rx="4" opacity="0.95" />
    <text x="30" y="35" font-family="system-ui" font-size="10" fill="var(--svg-text-primary)" font-weight="600">Distance: 0.8 mi</text>
    <text x="30" y="45" font-family="system-ui" font-size="10" fill="var(--svg-text-secondary)">Time: {estimatedTime}</text>
  )}
  
  <!-- Compass -->
  <circle cx="350" cy="50" r="20" fill="var(--svg-bg-primary)" stroke="var(--svg-neutral-300)" stroke-width="1" opacity="0.9" />
  <path d="M350 35 L355 45 L350 40 L345 45 Z" fill="var(--svg-error-500)" />
  <text x="350" y="70" font-family="system-ui" font-size="8" fill="var(--svg-text-tertiary)" text-anchor="middle">N</text>
  
  <!-- Interactive hover overlay -->
  {interactive && (
    <rect x="0" y="0" width="400" height="300" rx="12" fill="var(--svg-primary-500)" opacity="0" class="hover:opacity-5 transition-opacity duration-300 cursor-pointer" />
  )}
</SVGWrapper>
