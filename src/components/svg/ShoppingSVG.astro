---
import SVGWrapper from './SVGWrapper.astro';

export interface Props {
  class?: string;
  businessName?: string;
  interactive?: boolean;
}

const {
  class: className = "w-full h-full",
  businessName = "Business Chat",
  interactive = false
} = Astro.props;
---

<SVGWrapper 
  class={className}
  viewBox="0 0 360 640"
>
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="shopping-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--svg-primary-500)" stop-opacity="0.1"/>
      <stop offset="100%" stop-color="var(--svg-primary-500)" stop-opacity="0.3"/>
    </linearGradient>
    
    <linearGradient id="message-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--svg-bg-primary)"/>
      <stop offset="100%" stop-color="var(--svg-bg-secondary)"/>
    </linearGradient>
    
    <filter id="card-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
      <feOffset dx="0" dy="2"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.2"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Background -->
  <rect width="360" height="640" fill="var(--svg-bg-secondary)"/>

  <!-- Header -->
  <rect x="0" y="0" width="360" height="80" fill="var(--svg-primary-500)"/>
  <circle cx="40" cy="40" r="20" fill="var(--svg-bg-primary)" opacity="0.9"/>
  <text x="80" y="45" fill="var(--svg-bg-primary)" font-family="system-ui" font-size="18" font-weight="600">{businessName}</text>
  <text x="80" y="65" fill="var(--svg-bg-primary)" opacity="0.8" font-family="system-ui" font-size="12">Online</text>

  <!-- AI Assistant Welcome Message -->
  <g transform="translate(16, 96)">
    <rect width="280" height="80" rx="16" fill="url(#message-bg)" filter="url(#card-shadow)"/>
    <text x="20" y="25" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Hi! I'm your AI shopping assistant 🤖</text>
    <text x="20" y="45" font-family="system-ui" font-size="14" fill="var(--svg-text-secondary)">I can help you find products, check inventory,</text>
    <text x="20" y="65" font-family="system-ui" font-size="14" fill="var(--svg-text-secondary)">and complete your purchase instantly!</text>
  </g>

  <!-- Smart Quick Reply Buttons -->
  <g transform="translate(16, 190)">
    <rect width="100" height="36" rx="18" fill="var(--svg-primary-500)" filter="url(#card-shadow)"/>
    <text x="50" y="24" font-family="system-ui" font-size="13" fill="var(--svg-bg-primary)" text-anchor="middle">🛍️ Shop Now</text>
  </g>
  <g transform="translate(126, 190)">
    <rect width="100" height="36" rx="18" fill="var(--svg-accent-500)" filter="url(#card-shadow)"/>
    <text x="50" y="24" font-family="system-ui" font-size="13" fill="var(--svg-text-primary)" text-anchor="middle">🎯 Deals</text>
  </g>
  <g transform="translate(236, 190)">
    <rect width="100" height="36" rx="18" fill="var(--svg-secondary-300)" filter="url(#card-shadow)"/>
    <text x="50" y="24" font-family="system-ui" font-size="13" fill="var(--svg-text-primary)" text-anchor="middle">💬 Help</text>
  </g>

  <!-- AI-Powered Product Recommendations -->
  <g transform="translate(16, 250)">
    <rect width="328" height="220" rx="16" fill="var(--svg-bg-primary)" filter="url(#card-shadow)" stroke="var(--svg-primary-200)" stroke-width="1"/>

    <!-- Recommendation Header -->
    <rect x="16" y="16" width="296" height="30" rx="8" fill="var(--svg-primary-50)" />
    <text x="24" y="28" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)" font-weight="500">🤖 AI Recommendations</text>
    <text x="24" y="40" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" font-weight="600">Based on your preferences</text>

    <!-- Enhanced Product Cards -->
    <g transform="translate(20, 55)">
      <rect width="135" height="150" rx="12" fill="var(--svg-bg-primary)" filter="url(#card-shadow)" stroke="var(--svg-neutral-200)" stroke-width="1"/>
      <rect x="8" y="8" width="119" height="80" rx="8" fill="var(--svg-primary-50)"/>

      <!-- Smart Watch Icon -->
      <g transform="translate(55, 25)">
        <circle r="18" fill="var(--svg-primary-200)" stroke="var(--svg-primary-400)" stroke-width="2"/>
        <rect x="-8" y="-8" width="16" height="16" rx="3" fill="var(--svg-primary-500)"/>
        <circle r="6" fill="var(--svg-bg-primary)"/>
        <rect x="-1" y="-3" width="2" height="6" fill="var(--svg-primary-500)"/>
      </g>

      <text x="67" y="110" font-family="system-ui" font-size="13" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Smart Watch Pro</text>
      <text x="67" y="125" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)" text-anchor="middle">Health tracking</text>
      <text x="67" y="145" font-family="system-ui" font-size="14" fill="var(--svg-primary-600)" text-anchor="middle" font-weight="bold">$299.99</text>
    </g>

    <g transform="translate(170, 55)">
      <rect width="135" height="150" rx="12" fill="var(--svg-bg-primary)" filter="url(#card-shadow)" stroke="var(--svg-neutral-200)" stroke-width="1"/>
      <rect x="8" y="8" width="119" height="80" rx="8" fill="var(--svg-accent-50)"/>

      <!-- Headphones Icon -->
      <g transform="translate(67, 48)">
        <path d="M-15 -10 C-15 -20 -8 -25 0 -25 C8 -25 15 -20 15 -10 L15 5 C15 10 12 15 8 15 L-8 15 C-12 15 -15 10 -15 5 Z" fill="var(--svg-accent-400)"/>
        <rect x="-18" y="-5" width="8" height="15" rx="4" fill="var(--svg-accent-500)"/>
        <rect x="10" y="-5" width="8" height="15" rx="4" fill="var(--svg-accent-500)"/>
        <path d="M-15 -10 L15 -10" stroke="var(--svg-accent-600)" stroke-width="3" stroke-linecap="round"/>
      </g>

      <text x="67" y="110" font-family="system-ui" font-size="13" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Wireless Headphones</text>
      <text x="67" y="125" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)" text-anchor="middle">Noise cancelling</text>
      <text x="67" y="145" font-family="system-ui" font-size="14" fill="var(--svg-accent-600)" text-anchor="middle" font-weight="bold">$149.99</text>
    </g>
  </g>

  <!-- Smart Action Buttons -->
  <g transform="translate(16, 485)">
    <rect width="155" height="44" rx="22" fill="var(--svg-primary-500)" filter="url(#card-shadow)"/>
    <text x="77" y="28" font-family="system-ui" font-size="14" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="600">🛒 Add to Cart</text>

    <rect x="173" y="0" width="155" height="44" rx="22" fill="var(--svg-accent-500)" filter="url(#card-shadow)"/>
    <text x="250" y="28" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">💳 Buy Now</text>

    <!-- Interactive pulse effect -->
    {interactive && (
      <>
        <rect width="155" height="44" rx="22" fill="var(--svg-primary-500)" opacity="0.3" class="animate-pulse"/>
        <rect x="173" y="0" width="155" height="44" rx="22" fill="var(--svg-accent-500)" opacity="0.3" class="animate-pulse" style="animation-delay: 0.5s"/>
      </>
    )}
  </g>

  <!-- Customer Message -->
  <g transform="translate(80, 545)">
    <rect width="264" height="50" rx="16" fill="var(--svg-secondary-100)" filter="url(#card-shadow)"/>
    <text x="20" y="25" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Can you tell me more about the smart watch?</text>
    <text x="20" y="40" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Does it track sleep patterns?</text>
  </g>

  <!-- AI Assistant Response -->
  <g transform="translate(16, 605)">
    <rect width="280" height="30" rx="15" fill="url(#message-bg)" filter="url(#card-shadow)"/>
    <text x="20" y="20" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Yes! It has advanced sleep tracking with AI insights...</text>

    <!-- Typing Indicator -->
    {interactive && (
      <g transform="translate(290, 8)">
        <circle cx="5" cy="7" r="2" fill="var(--svg-text-secondary)">
          <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0s"/>
        </circle>
        <circle cx="12" cy="7" r="2" fill="var(--svg-text-secondary)">
          <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.3s"/>
        </circle>
        <circle cx="19" cy="7" r="2" fill="var(--svg-text-secondary)">
          <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.6s"/>
        </circle>
      </g>
    )}
  </g>

  <!-- Interactive elements -->
  {interactive && (
    <>
      <!-- Floating notification -->
      <g transform="translate(300, 100)" class="animate-pulse">
        <circle cx="0" cy="0" r="8" fill="var(--svg-error-500)"/>
        <text x="0" y="4" font-family="system-ui" font-size="10" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="bold">3</text>
      </g>
      
      <!-- Shimmer effect on products -->
      <rect x="32" y="256" width="140" height="168" rx="12" fill="url(#shopping-bg)" opacity="0.3" class="animate-pulse"/>
      <rect x="188" y="256" width="140" height="168" rx="12" fill="url(#shopping-bg)" opacity="0.3" class="animate-pulse" style="animation-delay: 0.5s"/>
    </>
  )}

  <!-- Status indicators -->
  <g transform="translate(320, 20)">
    <circle cx="0" cy="0" r="4" fill="var(--svg-success-500)"/>
    {interactive && <circle cx="0" cy="0" r="8" fill="var(--svg-success-500)" opacity="0.3" class="animate-ping"/>}
  </g>
</SVGWrapper>
