---
import TicketSVG from '../svg/TicketSVG.astro';
import LocationMapSVG from '../svg/LocationMapSVG.astro';
import ShoppingSVG from '../svg/ShoppingSVG.astro';
---

<section id="use-cases" class="py-24 bg-gradient-to-br from-gray-50 via-primary-50/30 to-accent-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/30 dark:to-accent-900/30 rounded-full text-primary-700 dark:text-primary-300 text-sm font-medium mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
        Interactive Business Experiences
      </div>
      <h2 class="text-3xl md:text-5xl font-bold text-gray-900 dark:text-white leading-tight">
        See What AI-Powered RCS
        <span class="bg-gradient-to-r from-primary-600 via-accent-500 to-primary-700 bg-clip-text text-transparent">Can Do</span>
      </h2>
      <p class="mt-6 text-xl text-gray-600 dark:text-gray-400 max-w-4xl mx-auto leading-relaxed">
        Real examples of how businesses use our AI-enhanced platform to create engaging, conversion-focused customer experiences
      </p>
    </div>

    <!-- Interactive Examples Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-24">

      <!-- Digital Tickets Experience -->
      <div class="group">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-2xl hover:scale-[1.02]">
          <!-- Header -->
          <div class="bg-gradient-to-r from-accent-500 to-accent-600 px-6 py-4">
            <div class="flex items-center justify-between">
              <h3 class="text-xl font-bold text-white">Digital Tickets & Passes</h3>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span class="text-white/90 text-sm">RCS Demo</span>
              </div>
            </div>
          </div>

          <!-- Demo Content -->
          <div class="p-6">
            <p class="text-gray-600 dark:text-gray-400 mb-6">
              Send interactive, scannable tickets directly to customers' messaging apps. No separate apps needed.
            </p>

            <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-xl p-4 mb-6">
              <TicketSVG
                class="w-full"
                eventName="SUMMER MUSIC FESTIVAL"
                eventDate="July 15-17, 2025"
                eventLocation="Central Park"
                ticketType="3-Day Pass • General Admission"
                ticketNumber="VIP-2025-78945"
                holderName="Sarah Johnson"
                gate="GATE 4"
                section="SECTION A"
                interactive={true}
              />
            </div>

            <!-- Features List -->
            <div class="space-y-3">
              <div class="flex items-center space-x-3">
                <div class="w-5 h-5 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Scannable QR codes for instant entry</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-5 h-5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">One-tap add to digital wallet</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-5 h-5 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Real-time updates and notifications</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Smart Navigation Experience -->
      <div class="group">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-2xl hover:scale-[1.02]">
          <!-- Header -->
          <div class="bg-gradient-to-r from-secondary-500 to-secondary-600 px-6 py-4">
            <div class="flex items-center justify-between">
              <h3 class="text-xl font-bold text-white">AI-Powered Navigation</h3>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span class="text-white/90 text-sm">Live Demo</span>
              </div>
            </div>
          </div>

          <!-- Demo Content -->
          <div class="p-6">
            <p class="text-gray-600 dark:text-gray-400 mb-6">
              Help customers find your business with intelligent maps, real-time directions, and contextual information.
            </p>

            <!-- Conversation Preview -->
            <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-xl p-4 mb-6 space-y-3">
              <div class="flex items-start">
                <div class="bg-white dark:bg-gray-600 rounded-lg p-3 max-w-[75%] shadow-sm">
                  <p class="text-gray-800 dark:text-gray-200 text-sm">Where are you located?</p>
                </div>
              </div>

              <div class="flex items-start justify-end">
                <div class="bg-primary-500 rounded-lg p-3 max-w-[75%] shadow-sm">
                  <p class="text-white text-sm">Here's our location with smart directions:</p>
                </div>
              </div>

              <!-- Interactive Map -->
              <div class="flex justify-end">
                <div class="bg-primary-500 rounded-lg p-3 max-w-[85%] shadow-sm">
                  <LocationMapSVG
                    class="w-full h-40"
                    businessName="Our Store"
                    interactive={true}
                    showRoute={true}
                    estimatedTime="15 min"
                  />
                  <div class="mt-2 text-white text-xs">
                    <p>📍 Open until 8:00 PM • ETA: 15 min</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Features List -->
            <div class="space-y-3">
              <div class="flex items-center space-x-3">
                <div class="w-5 h-5 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Real-time traffic & optimal routing</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-5 h-5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">One-tap Google Maps integration</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-5 h-5 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Business hours & contact info</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Shopping Experience Section -->
    <div class="mt-24">
      <div class="text-center mb-16">
        <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/30 dark:to-accent-900/30 rounded-full text-primary-700 dark:text-primary-300 text-sm font-medium mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
          AI-Enhanced Shopping
        </div>
        <h3 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
          Interactive Product
          <span class="bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent">Experiences</span>
        </h3>
        <p class="text-xl text-gray-600 dark:text-gray-400 max-w-4xl mx-auto leading-relaxed">
          Transform e-commerce with rich product catalogs, AI recommendations, and seamless purchasing - all within RCS messaging
        </p>
      </div>

      <!-- Shopping Demo Card -->
      <div class="max-w-5xl mx-auto">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700">
          <!-- Header -->
          <div class="bg-gradient-to-r from-primary-500 to-primary-600 px-6 py-4">
            <div class="flex items-center justify-between">
              <h4 class="text-xl font-bold text-white">AI Shopping Assistant</h4>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span class="text-white/90 text-sm">Live RCS Demo</span>
              </div>
            </div>
          </div>

          <!-- Demo Content -->
          <div class="p-8">
            <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-xl p-6">
              <ShoppingSVG class="w-full h-96" interactive={true} />
            </div>
          </div>
        </div>
      </div>

      <!-- Features Summary -->
      <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </div>
          <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Rich Product Catalogs</h4>
          <p class="text-gray-600 dark:text-gray-400 text-sm">Interactive browsing with high-quality images, detailed descriptions, and instant purchasing</p>
        </div>

        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-r from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
          <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-2">AI Recommendations</h4>
          <p class="text-gray-600 dark:text-gray-400 text-sm">Personalized product suggestions based on customer behavior and preferences</p>
        </div>

        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-r from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
          </div>
          <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Secure Payments</h4>
          <p class="text-gray-600 dark:text-gray-400 text-sm">One-click purchasing with enterprise-grade security and payment processing</p>
        </div>
      </div>
    </div>
  </div>
</section>

