<!-- Features Schema -->
<script type="application/ld+json" is:inline>
{
  "@context": "https://schema.org",
  "@type": "ItemList",
  "name": "RCS Platform Features",
  "description": "Comprehensive list of RCS messaging platform features and capabilities",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "item": {
        "@type": "SoftwareFeature",
        "name": "Rich Media Support",
        "description": "Send images, videos, audio files, and documents with high-quality delivery"
      }
    },
    {
      "@type": "ListItem",
      "position": 2,
      "item": {
        "@type": "SoftwareFeature",
        "name": "Interactive Buttons",
        "description": "Add clickable buttons for calls-to-action, quick replies, and navigation"
      }
    },
    {
      "@type": "ListItem",
      "position": 3,
      "item": {
        "@type": "SoftwareFeature",
        "name": "Message Carousels",
        "description": "Create scrollable product catalogs and interactive content galleries"
      }
    },
    {
      "@type": "ListItem",
      "position": 4,
      "item": {
        "@type": "SoftwareFeature",
        "name": "Real-time Analytics",
        "description": "Track delivery, read receipts, and engagement metrics in real-time"
      }
    },
    {
      "@type": "ListItem",
      "position": 5,
      "item": {
        "@type": "SoftwareFeature",
        "name": "Enterprise Security",
        "description": "End-to-end encryption and enterprise-grade security compliance"
      }
    },
    {
      "@type": "ListItem",
      "position": 6,
      "item": {
        "@type": "SoftwareFeature",
        "name": "Global Reach",
        "description": "Worldwide carrier support with 99.9% delivery reliability"
      }
    }
  ]
}
</script>

<section id="features" class="py-20 bg-white dark:bg-gray-900">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section header -->
    <div class="max-w-4xl mx-auto text-center mb-16">
      <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/30 dark:to-accent-900/30 rounded-full text-primary-700 dark:text-primary-300 text-sm font-medium mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
        AI-Enhanced Platform Features
      </div>
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
        AI-Enhanced RCS Messaging
        <span class="bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent">Platform</span>
      </h2>
      <p class="mt-6 text-xl text-gray-600 dark:text-gray-400 leading-relaxed">
        Rich Communication Services (RCS) powered by AI - delivering interactive media, smart buttons, and intelligent analytics that transform customer engagement
      </p>
    </div>
    
    <!-- Features grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
      <!-- Feature 1 - Rich Media Messaging -->
      <div class="bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 rounded-xl p-8 transition-all hover:shadow-lg reveal border border-primary-200 dark:border-primary-800">
        <div class="w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Rich Media Messaging</h3>
        <p class="mt-4 text-gray-600 dark:text-gray-400">
          Send high-quality images, videos, audio files, and interactive carousels that engage customers far beyond traditional SMS capabilities.
        </p>
        <div class="mt-4 flex items-center text-sm text-primary-700 dark:text-primary-300">
          <span class="bg-primary-200 dark:bg-primary-800 px-2 py-1 rounded-full text-xs font-medium">AI-Optimized</span>
        </div>
        <a href="/docs/features/rich-media" class="mt-6 inline-flex items-center text-primary-600 dark:text-primary-400 font-medium">
          Learn more
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>

      <!-- Feature 2 - Interactive Buttons -->
      <div class="bg-gradient-to-br from-accent-50 to-accent-100 dark:from-accent-900/20 dark:to-accent-800/20 rounded-xl p-8 transition-all hover:shadow-lg reveal border border-accent-200 dark:border-accent-800">
        <div class="w-12 h-12 bg-gradient-to-r from-accent-500 to-accent-600 rounded-lg flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Interactive Buttons & Actions</h3>
        <p class="mt-4 text-gray-600 dark:text-gray-400">
          Add clickable buttons for calls-to-action, quick replies, website links, and phone calls - all tracked with AI-powered analytics.
        </p>
        <div class="mt-4 flex items-center text-sm text-accent-700 dark:text-accent-300">
          <span class="bg-accent-200 dark:bg-accent-800 px-2 py-1 rounded-full text-xs font-medium">Smart Tracking</span>
        </div>
        <a href="/docs/features/interactive-buttons" class="mt-6 inline-flex items-center text-accent-600 dark:text-accent-400 font-medium">
          Learn more
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>

      <!-- Feature 3 - AI Analytics -->
      <div class="bg-gradient-to-br from-secondary-50 to-secondary-100 dark:from-secondary-900/20 dark:to-secondary-800/20 rounded-xl p-8 transition-all hover:shadow-lg reveal border border-secondary-200 dark:border-secondary-800">
        <div class="w-12 h-12 bg-gradient-to-r from-secondary-500 to-secondary-600 rounded-lg flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">AI-Powered Analytics</h3>
        <p class="mt-4 text-gray-600 dark:text-gray-400">
          Track delivery, read receipts, button interactions, and customer engagement with intelligent insights and predictive recommendations.
        </p>
        <div class="mt-4 flex items-center text-sm text-secondary-700 dark:text-secondary-300">
          <span class="bg-secondary-200 dark:bg-secondary-800 px-2 py-1 rounded-full text-xs font-medium">Predictive Insights</span>
        </div>
        <a href="/docs/features/analytics" class="mt-6 inline-flex items-center text-secondary-600 dark:text-secondary-400 font-medium">
          Learn more
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
      
      <!-- Feature 4 - Transactional Messaging -->
      <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-8 transition-all hover:shadow-lg reveal border border-green-200 dark:border-green-800">
        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Rich Transactional Messages</h3>
        <p class="mt-4 text-gray-600 dark:text-gray-400">
          Transform order confirmations, shipping updates, and receipts into interactive RCS experiences with tracking, actions, and rich media.
        </p>
        <div class="mt-4 flex items-center text-sm text-green-700 dark:text-green-300">
          <span class="bg-green-200 dark:bg-green-800 px-2 py-1 rounded-full text-xs font-medium">Enterprise Ready</span>
        </div>
        <a href="/docs/features/transactional" class="mt-6 inline-flex items-center text-green-600 dark:text-green-400 font-medium">
          Learn more
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>

      <!-- Feature 5 - AI Chatbots -->
      <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-8 transition-all hover:shadow-lg reveal border border-purple-200 dark:border-purple-800">
        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">AI-Powered Chatbots</h3>
        <p class="mt-4 text-gray-600 dark:text-gray-400">
          Deploy intelligent conversational AI that understands context, handles complex queries, and seamlessly integrates with RCS rich features.
        </p>
        <div class="mt-4 flex items-center text-sm text-purple-700 dark:text-purple-300">
          <span class="bg-purple-200 dark:bg-purple-800 px-2 py-1 rounded-full text-xs font-medium">Context-Aware</span>
        </div>
        <a href="/docs/features/chatbots" class="mt-6 inline-flex items-center text-purple-600 dark:text-purple-400 font-medium">
          Learn more
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>

      <!-- Feature 6 - Enterprise Security -->
      <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 rounded-xl p-8 transition-all hover:shadow-lg reveal border border-indigo-200 dark:border-indigo-800">
        <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Enterprise Security</h3>
        <p class="mt-4 text-gray-600 dark:text-gray-400">
          Bank-grade encryption, GDPR compliance, SOC 2 certification, and enterprise-level security controls for all RCS communications.
        </p>
        <div class="mt-4 flex items-center text-sm text-indigo-700 dark:text-indigo-300">
          <span class="bg-indigo-200 dark:bg-indigo-800 px-2 py-1 rounded-full text-xs font-medium">SOC 2 Certified</span>
        </div>
        <a href="/docs/features/security" class="mt-6 inline-flex items-center text-indigo-600 dark:text-indigo-400 font-medium">
          Learn more
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
    </div>
  </div>
</section>